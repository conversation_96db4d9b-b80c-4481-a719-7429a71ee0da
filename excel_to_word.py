#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据提取并转换为Word文档脚本
从Excel文件中提取公司信息并格式化为Word文档
"""

import pandas as pd
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import sys
import os

def read_excel_data(excel_file):
    """
    读取Excel文件数据
    新格式：A列是城市名，B列公司名，C列电话，D列邮箱，E列官网，F列微信，G列地址

    Args:
        excel_file (str): Excel文件路径

    Returns:
        list: 包含城市和公司信息的列表，格式为 [{'city': '城市名', 'companies': [公司信息列表]}]
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file, header=None)  # 不使用第一行作为列名

        # 确保有足够的列
        if df.shape[1] < 7:
            raise ValueError(f"Excel文件列数不足，需要至少7列，当前只有{df.shape[1]}列")

        cities_data = []
        current_city = None
        current_companies = []

        # 遍历每一行
        for index, row in df.iterrows():
            # 检查A列是否有城市名（非空且不是公司数据）
            cell_a = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
            cell_b = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""

            # 如果A列有内容且B列为空或者A列内容看起来像城市名，则认为是城市行
            if cell_a and (not cell_b or len(cell_a) <= 10):  # 假设城市名不超过10个字符
                # 保存之前的城市数据
                if current_city and current_companies:
                    cities_data.append({
                        'city': current_city,
                        'companies': current_companies.copy()
                    })

                # 开始新城市
                current_city = cell_a
                current_companies = []

            elif cell_b:  # B列有公司名，这是公司数据行
                company_info = {
                    '公司名': cell_b,
                    '电话': str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else "",
                    '邮箱': str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else "",
                    '官网': str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else "",
                    '微信': str(row.iloc[5]).strip() if pd.notna(row.iloc[5]) else "",
                    '地址': str(row.iloc[6]).strip() if pd.notna(row.iloc[6]) else ""
                }

                if current_city:
                    current_companies.append(company_info)

        # 保存最后一个城市的数据
        if current_city and current_companies:
            cities_data.append({
                'city': current_city,
                'companies': current_companies.copy()
            })

        return cities_data

    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def create_word_document(data_df, output_file):
    """
    创建Word文档
    
    Args:
        data_df (pandas.DataFrame): 包含公司信息的数据框
        output_file (str): 输出Word文件路径
    """
    try:
        # 创建新的Word文档
        doc = Document()
        
        # 设置文档标题
        title = doc.add_heading('留学机构联系信息', 0)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 遍历每一行数据
        for index, row in data_df.iterrows():
            company_name = str(row['公司名']).strip() if pd.notna(row['公司名']) else ""
            phone = str(row['电话']).strip() if pd.notna(row['电话']) else ""
            email = str(row['邮箱']).strip() if pd.notna(row['邮箱']) else ""
            address = str(row['地址']).strip() if pd.notna(row['地址']) else ""
            
            # 跳过空的公司名
            if not company_name:
                continue
            
            # 添加编号和公司名
            company_paragraph = doc.add_paragraph()
            company_paragraph.add_run(f"{index + 1}. {company_name}").bold = True
            
            # 添加地址（如果有）
            if address:
                address_paragraph = doc.add_paragraph()
                address_paragraph.add_run(f"地址：{address}")
            
            # 添加电话（如果有）
            if phone:
                phone_paragraph = doc.add_paragraph()
                phone_paragraph.add_run(f"电话：{phone}")
            
            # 添加邮箱（如果有）
            if email:
                email_paragraph = doc.add_paragraph()
                email_paragraph.add_run(f"邮箱：{email}")
            
            # 添加空行分隔
            doc.add_paragraph()
        
        # 保存文档
        doc.save(output_file)
        print(f"Word文档已成功创建: {output_file}")
        
    except Exception as e:
        print(f"创建Word文档时出错: {e}")

def main():
    """
    主函数
    """
    # 设置文件路径
    excel_file = "新建 Microsoft Excel 工作表.xlsx"
    output_file = "留学机构联系信息.docx"
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误：找不到Excel文件 '{excel_file}'")
        print("请确保Excel文件在当前目录下")
        return
    
    print(f"正在读取Excel文件: {excel_file}")
    
    # 读取Excel数据
    data = read_excel_data(excel_file)
    
    if data is None:
        print("读取Excel文件失败")
        return
    
    print(f"成功读取 {len(data)} 条记录")
    
    # 显示前几条数据预览
    print("\n数据预览:")
    print(data.head())
    
    # 创建Word文档
    print(f"\n正在创建Word文档: {output_file}")
    create_word_document(data, output_file)

if __name__ == "__main__":
    main()
