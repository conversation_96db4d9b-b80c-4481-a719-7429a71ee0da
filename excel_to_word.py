#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据提取并转换为Word文档脚本
从Excel文件中提取公司信息并格式化为Word文档
"""

import pandas as pd
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import sys
import os

def read_excel_data(excel_file):
    """
    读取Excel文件数据
    
    Args:
        excel_file (str): Excel文件路径
        
    Returns:
        pandas.DataFrame: 包含公司信息的数据框
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 确保有足够的列
        if df.shape[1] < 6:
            raise ValueError(f"Excel文件列数不足，需要至少6列，当前只有{df.shape[1]}列")
        
        # 选择需要的列：第1列(公司名)、第2列(电话)、第3列(邮箱)、第6列(地址)
        # 注意：pandas的列索引从0开始
        selected_columns = [0, 1, 2, 5]  # 对应第1、2、3、6列
        
        # 创建新的数据框，只包含需要的列
        result_df = df.iloc[:, selected_columns].copy()
        result_df.columns = ['公司名', '电话', '邮箱', '地址']
        
        # 删除空行
        result_df = result_df.dropna(subset=['公司名'])
        
        return result_df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def create_word_document(data_df, output_file):
    """
    创建Word文档
    
    Args:
        data_df (pandas.DataFrame): 包含公司信息的数据框
        output_file (str): 输出Word文件路径
    """
    try:
        # 创建新的Word文档
        doc = Document()
        
        # 设置文档标题
        title = doc.add_heading('留学机构联系信息', 0)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 遍历每一行数据
        for index, row in data_df.iterrows():
            company_name = str(row['公司名']).strip() if pd.notna(row['公司名']) else ""
            phone = str(row['电话']).strip() if pd.notna(row['电话']) else ""
            email = str(row['邮箱']).strip() if pd.notna(row['邮箱']) else ""
            address = str(row['地址']).strip() if pd.notna(row['地址']) else ""
            
            # 跳过空的公司名
            if not company_name:
                continue
            
            # 添加编号和公司名
            company_paragraph = doc.add_paragraph()
            company_paragraph.add_run(f"{index + 1}. {company_name}").bold = True
            
            # 添加地址（如果有）
            if address:
                address_paragraph = doc.add_paragraph()
                address_paragraph.add_run(f"地址：{address}")
            
            # 添加电话（如果有）
            if phone:
                phone_paragraph = doc.add_paragraph()
                phone_paragraph.add_run(f"电话：{phone}")
            
            # 添加邮箱（如果有）
            if email:
                email_paragraph = doc.add_paragraph()
                email_paragraph.add_run(f"邮箱：{email}")
            
            # 添加空行分隔
            doc.add_paragraph()
        
        # 保存文档
        doc.save(output_file)
        print(f"Word文档已成功创建: {output_file}")
        
    except Exception as e:
        print(f"创建Word文档时出错: {e}")

def main():
    """
    主函数
    """
    # 设置文件路径
    excel_file = "留学机构名单.xlsx"
    output_file = "留学机构联系信息.docx"
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误：找不到Excel文件 '{excel_file}'")
        print("请确保Excel文件在当前目录下")
        return
    
    print(f"正在读取Excel文件: {excel_file}")
    
    # 读取Excel数据
    data = read_excel_data(excel_file)
    
    if data is None:
        print("读取Excel文件失败")
        return
    
    print(f"成功读取 {len(data)} 条记录")
    
    # 显示前几条数据预览
    print("\n数据预览:")
    print(data.head())
    
    # 创建Word文档
    print(f"\n正在创建Word文档: {output_file}")
    create_word_document(data, output_file)

if __name__ == "__main__":
    main()
