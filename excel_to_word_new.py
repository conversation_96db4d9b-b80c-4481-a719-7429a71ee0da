#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据提取并转换为Word文档脚本
从Excel文件中提取公司信息并格式化为Word文档
新格式：A列空，B列开始是数据，当B列是城市名时，后续行是该城市的公司数据
"""

import pandas as pd
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import sys
import os

def read_excel_data(excel_file):
    """
    读取Excel文件数据
    新格式：A列空，B列开始是数据
    当B列是城市名时，后续行是该城市的公司数据
    B列=公司名，C列=电话，D列=邮箱，E列=官网，F列=微信，G列=地址
    
    Args:
        excel_file (str): Excel文件路径
        
    Returns:
        list: 包含城市和公司信息的列表，格式为 [{'city': '城市名', 'companies': [公司信息列表]}]
    """
    try:
        # 读取Excel文件，不使用第一行作为列名
        df = pd.read_excel(excel_file, header=None)
        
        # 确保有足够的列
        if df.shape[1] < 7:
            raise ValueError(f"Excel文件列数不足，需要至少7列(A-G)，当前只有{df.shape[1]}列")
        
        cities_data = []
        current_city = None
        current_companies = []
        
        # 常见城市名称列表，用于判断是否为城市行
        common_cities = ['北京', '上海', '广州', '深圳', '重庆', '天津', '成都', '武汉', 
                        '西安', '南京', '杭州', '青岛', '大连', '宁波', '厦门', '苏州',
                        '无锡', '福州', '济南', '哈尔滨', '长春', '沈阳', '石家庄',
                        '郑州', '太原', '合肥', '长沙', '南昌', '贵阳', '昆明', '南宁',
                        '海口', '三亚', '拉萨', '银川', '西宁', '乌鲁木齐', '呼和浩特']
        
        # 遍历每一行
        for index, row in df.iterrows():
            # B列的内容（索引1）
            cell_b = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
            
            if not cell_b:  # B列为空，跳过
                continue
            
            # 判断是否为城市行
            # 1. 检查是否在常见城市列表中
            # 2. 或者检查其他列是否大部分为空（城市行通常只有B列有内容）
            is_city_row = False
            
            if cell_b in common_cities:
                is_city_row = True
            else:
                # 检查C到G列是否大部分为空
                other_cols = [str(row.iloc[i]).strip() if pd.notna(row.iloc[i]) else "" 
                             for i in range(2, 7)]
                empty_count = sum(1 for col in other_cols if not col)
                if empty_count >= 4:  # 如果C到G列中有4列或以上为空，可能是城市行
                    is_city_row = True
            
            if is_city_row:
                # 保存之前的城市数据
                if current_city and current_companies:
                    cities_data.append({
                        'city': current_city,
                        'companies': current_companies.copy()
                    })
                
                # 开始新城市
                current_city = cell_b
                current_companies = []
                print(f"发现城市: {current_city}")
                
            else:
                # 这是公司数据行
                company_info = {
                    '公司名': cell_b,
                    '电话': str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else "",
                    '邮箱': str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else "",
                    '官网': str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else "",
                    '微信': str(row.iloc[5]).strip() if pd.notna(row.iloc[5]) else "",
                    '地址': str(row.iloc[6]).strip() if pd.notna(row.iloc[6]) else ""
                }
                
                if current_city:
                    current_companies.append(company_info)
                    print(f"  添加公司: {cell_b}")
        
        # 保存最后一个城市的数据
        if current_city and current_companies:
            cities_data.append({
                'city': current_city,
                'companies': current_companies.copy()
            })
        
        return cities_data
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def create_word_document(cities_data, output_file):
    """
    创建Word文档
    
    Args:
        cities_data (list): 包含城市和公司信息的列表
        output_file (str): 输出Word文件路径
    """
    try:
        # 创建新的Word文档
        doc = Document()
        
        # 设置文档标题
        title = doc.add_heading('留学机构联系信息', 0)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        company_counter = 1
        
        # 遍历每个城市
        for city_data in cities_data:
            city_name = city_data['city']
            companies = city_data['companies']
            
            # 添加城市标题
            city_heading = doc.add_heading(f'{city_name}地区', level=1)
            
            # 遍历该城市的每个公司
            for company in companies:
                company_name = company['公司名']
                phone = company['电话']
                email = company['邮箱']
                website = company['官网']
                wechat = company['微信']
                address = company['地址']
                
                # 跳过空的公司名
                if not company_name:
                    continue
                
                # 添加编号和公司名
                company_paragraph = doc.add_paragraph()
                company_paragraph.add_run(f"{company_counter}. {company_name}").bold = True
                
                # 添加地址（如果有）
                if address:
                    address_paragraph = doc.add_paragraph()
                    address_paragraph.add_run(f"地址：{address}")
                
                # 添加电话（如果有）
                if phone:
                    phone_paragraph = doc.add_paragraph()
                    phone_paragraph.add_run(f"电话：{phone}")
                
                # 添加邮箱（如果有）
                if email:
                    email_paragraph = doc.add_paragraph()
                    email_paragraph.add_run(f"邮箱：{email}")
                
                # 添加官网（如果有）
                if website:
                    website_paragraph = doc.add_paragraph()
                    website_paragraph.add_run(f"官网：{website}")
                
                # 添加微信（如果有）
                if wechat:
                    wechat_paragraph = doc.add_paragraph()
                    wechat_paragraph.add_run(f"微信：{wechat}")
                
                # 添加空行分隔
                doc.add_paragraph()
                
                company_counter += 1
        
        # 保存文档
        doc.save(output_file)
        print(f"Word文档已成功创建: {output_file}")
        
    except Exception as e:
        print(f"创建Word文档时出错: {e}")

def main():
    """
    主函数
    """
    # 设置文件路径
    excel_file = "留学机构名单.xlsx"
    output_file = "留学机构联系信息.docx"
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误：找不到Excel文件 '{excel_file}'")
        print("请确保Excel文件在当前目录下")
        return
    
    print(f"正在读取Excel文件: {excel_file}")
    
    # 读取Excel数据
    cities_data = read_excel_data(excel_file)
    
    if cities_data is None:
        print("读取Excel文件失败")
        return
    
    total_companies = sum(len(city_data['companies']) for city_data in cities_data)
    print(f"成功读取 {len(cities_data)} 个城市，共 {total_companies} 家公司")
    
    # 显示数据预览
    print("\n数据预览:")
    for city_data in cities_data[:3]:  # 只显示前3个城市
        print(f"城市: {city_data['city']}")
        for company in city_data['companies'][:2]:  # 每个城市只显示前2家公司
            print(f"  公司: {company['公司名']}")
        if len(city_data['companies']) > 2:
            print(f"  ... 还有 {len(city_data['companies']) - 2} 家公司")
        print()
    
    # 创建Word文档
    print(f"正在创建Word文档: {output_file}")
    create_word_document(cities_data, output_file)

if __name__ == "__main__":
    main()
